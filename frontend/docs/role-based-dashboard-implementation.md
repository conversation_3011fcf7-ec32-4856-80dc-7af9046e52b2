# Role-Based Dashboard Implementation

## Overview

This document describes the implementation of role-based dashboard functionality for the Trodoo application. The system supports three user roles: **Renter**, **Venue Owner**, and **Admin**, with support for dual-role users who can be both renters and venue owners.

## Architecture

### Core Components

1. **Role Detection System** (`lib/roleUtils.ts`)
   - Detects user roles from explicit role assignments and implicit behavior (venue ownership)
   - Supports async role detection with venue and booking data
   - Provides utility functions for role checking and feature access

2. **Role-Specific Dashboard Components**
   - `RenterDashboard.tsx` - For users who book venues
   - `OwnerDashboard.tsx` - For users who list venues
   - `AdminDashboard.tsx` - For system administrators (enhanced existing component)

3. **Unified Dashboard** (`UnifiedDashboard.tsx`)
   - Main dashboard component that orchestrates role-based rendering
   - Handles role detection, loading states, and error handling
   - Supports role switching for dual-role users

4. **Role Switcher** (`RoleSwitcher.tsx`)
   - UI component for dual-role users to switch between roles
   - Persists role preference in localStorage
   - Compact and full versions available

### Role Detection Logic

#### Explicit Roles
- Stored in `user.roles` array field in PocketBase
- Default role: `['renter']` for new users
- Possible values: `'renter'`, `'owner'`, `'admin'`

#### Implicit Role Detection
- **Venue Owner**: Users who own venues (determined by `venues.owner` field)
- **Active Renter**: Users with booking history as renter
- **Active Owner**: Users with booking history as venue owner

#### Role Priority
1. **Admin**: Always takes precedence
2. **Owner**: If user has venues or owner bookings
3. **Renter**: Default role or if user has renter bookings

### Dashboard Features by Role

#### Renter Dashboard
- **Stats**: Total bookings, upcoming bookings, completed bookings, total spent
- **Features**: 
  - Recent bookings list
  - Quick actions (browse venues, favorites, profile)
  - Venue discovery recommendations
- **Navigation**: Browse venues, my bookings, favorites

#### Owner Dashboard  
- **Stats**: Total venues, pending requests, monthly earnings, average rating
- **Features**:
  - Recent booking requests
  - Quick actions (add venue, manage venues, analytics)
  - Performance summary
- **Navigation**: My venues, booking requests, analytics

#### Admin Dashboard
- **Stats**: Total users, venues, pending flags, bookings
- **Features**:
  - Recent activity feed
  - Quick actions (manage users, review content)
  - System status monitoring
- **Navigation**: Admin dashboard, user management, content moderation

#### Dual-Role Users
- **Role Switcher**: Toggle between renter and owner views
- **Combined View**: Option to see both dashboards in one view
- **Preference Persistence**: Remembers last selected role

## Implementation Details

### State Management

Enhanced the existing state management in `lib/state.ts`:

```typescript
// New stores for role detection
export const userRoleInfoStore = atom<UserRoleInfo | null>(null);
export const roleDetectionLoadingStore = atom<boolean>(false);

// New auth actions
authActions.detectUserRoles(): Promise<UserRoleInfo | null>
authActions.getQuickRoleInfo(): UserRoleInfo
```

### Navigation Updates

Updated `ProfileDropdown.tsx` to show role-appropriate navigation items:
- Dynamic menu items based on user roles
- Icon mapping for different navigation types
- Maintains existing profile and logout functionality

### Dashboard Page Integration

Modified `/dashboard/index.astro`:
- Replaced static dashboard content with `UnifiedDashboard` component
- Maintains existing authentication checks and app wrapper
- Preserves welcome banner and review prompts functionality

## Usage Examples

### Basic Role Detection
```typescript
import { detectUserRoles } from '../lib/roleUtils.ts';

const roleInfo = await detectUserRoles(user);
if (roleInfo.isOwner) {
  // Show owner features
}
```

### Role-Based Feature Access
```typescript
import { getDashboardFeatures } from '../lib/roleUtils.ts';

const features = getDashboardFeatures(roleInfo);
if (features.showRoleSwitcher) {
  // Show role switcher for dual-role users
}
```

### Navigation Items
```typescript
import { getNavigationItems } from '../lib/roleUtils.ts';

const navItems = getNavigationItems(roleInfo);
// Returns appropriate navigation items for user's roles
```

## Testing

Created comprehensive test suite in `lib/__tests__/roleUtils.test.ts`:
- Tests for explicit role detection
- Tests for dashboard feature configuration
- Tests for navigation item generation
- Tests for edge cases (null users, empty roles)

## Migration Notes

### Existing Users
- Users with no explicit roles get default `['renter']` role
- Venue owners are automatically detected and can access owner features
- No data migration required - roles are detected dynamically

### Backward Compatibility
- Existing `/dashboard/my-venues` page continues to work
- All existing authentication and authorization logic preserved
- Existing booking and venue management functionality unchanged

## Future Enhancements

1. **Role Assignment UI**: Admin interface to manually assign roles
2. **Role-Based Permissions**: Fine-grained permissions within roles
3. **Custom Dashboards**: User-configurable dashboard layouts
4. **Role Analytics**: Track role usage and feature adoption
5. **Notification Preferences**: Role-specific notification settings

## Configuration

### Environment Variables
No new environment variables required. Uses existing PocketBase configuration.

### Feature Flags
Role-based features are enabled by default. To disable:
- Remove `UnifiedDashboard` and restore original dashboard content
- Revert `ProfileDropdown` navigation changes

## Performance Considerations

- Role detection is cached in global state to avoid repeated API calls
- Lazy loading of role-specific components
- Efficient role checking using computed stores
- Minimal impact on existing dashboard load times

## Security Notes

- Role detection uses existing PocketBase authentication
- No client-side role elevation possible
- Server-side validation still required for sensitive operations
- Admin features protected by existing middleware
