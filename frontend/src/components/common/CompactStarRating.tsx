import React from 'react';
import StarRating from './StarRating';

export function CompactStarRating({ 
  rating, 
  reviewCount 
}: { 
  rating: number; 
  reviewCount?: number; 
}) {
  return (
    <div className="flex items-center gap-1">
      <StarRating rating={rating} size="sm" readonly />
      <span className="text-sm text-gray-600">
        {rating.toFixed(1)}
        {reviewCount !== undefined && ` (${reviewCount})`}
      </span>
    </div>
  );
}
