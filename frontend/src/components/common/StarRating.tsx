import React, { useState, useMemo } from 'react';
import { Star } from 'lucide-react';
import { cn } from '../../lib/utils.ts';

interface StarRatingProps {
  rating: number;
  onRatingChange?: (rating: number) => void;
  maxRating?: number;
  size?: 'sm' | 'md' | 'lg';
  readonly?: boolean;
  showValue?: boolean;
  className?: string;
}

const starSizes = {
  sm: 'h-4 w-4',
  md: 'h-5 w-5',
  lg: 'h-6 w-6',
};

const MemoizedStar = React.memo(({
  starValue,
  displayRating,
  size,
  readonly,
  handleStarClick,
  handleStarHover,
}: {
  starValue: number;
  displayRating: number;
  size: 'sm' | 'md' | 'lg';
  readonly: boolean;
  handleStarClick: (starValue: number) => void;
  handleStarHover: (starValue: number) => void;
}) => {
  const isFilled = starValue <= displayRating;
  const partialPercentage = useMemo(() => {
    const partial = displayRating % 1;
    const isPartial = !Number.isInteger(displayRating) && starValue === Math.ceil(displayRating);
    return isPartial ? 100 - partial * 100 : 0;
  }, [displayRating, starValue]);

  const isPartiallyFilled = partialPercentage > 0;

  return (
    <button
      type="button"
      data-star={starValue}
      className={cn(
        'relative transition-all duration-200',
        !readonly && 'hover:scale-110 cursor-pointer',
        readonly && 'cursor-default'
      )}
      onClick={() => handleStarClick(starValue)}
      onMouseEnter={() => handleStarHover(starValue)}
      disabled={readonly}
    >
      {/* Background star (empty) */}
      <Star
        className={cn(starSizes[size], 'text-gray-300')}
        fill="currentColor"
      />

      {/* Filled star overlay */}
      {(isFilled || isPartiallyFilled) && (
        <Star
          className={cn(starSizes[size], 'absolute inset-0 text-secondary-500')}
          fill="currentColor"
          style={isPartiallyFilled ? { clipPath: `inset(0 ${partialPercentage}% 0 0)` } : undefined}
        />
      )}
    </button>
  );
});

export default function StarRating({
  rating,
  onRatingChange,
  maxRating = 5,
  size = 'md',
  readonly = false,
  showValue = false,
  className,
}: StarRatingProps) {
  const [hoverRating, setHoverRating] = useState(0);

  const handleStarClick = (starValue: number) => {
    if (!readonly && onRatingChange) {
      onRatingChange(starValue);
    }
  };

  const handleStarHover = (starValue: number) => {
    if (!readonly) {
      setHoverRating(starValue);
    }
  };

  const handleMouseLeave = () => {
    if (!readonly) {
      setHoverRating(0);
    }
  };

  const displayRating = hoverRating || rating;

  return (
    <div className={cn('flex items-center gap-1', className)}>
      <div
        className="flex items-center"
        onMouseLeave={handleMouseLeave}
      >
        {Array.from({ length: maxRating }, (_, index) => (
          <MemoizedStar
            key={index}
            starValue={index + 1}
            displayRating={displayRating}
            size={size}
            readonly={readonly}
            handleStarClick={handleStarClick}
            handleStarHover={handleStarHover}
          />
        ))}
      </div>

      {showValue && (
        <span className="ml-2 text-sm text-gray-600">
          {rating.toFixed(1)} / {maxRating}
        </span>
      )}
    </div>
  );
}


