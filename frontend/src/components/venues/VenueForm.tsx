"use client"

import React, { useState, use<PERSON><PERSON>back, useEffect } from "react"
import type { VenueFormData, VenueAddress, VenueValidationErrors, ChecklistItem } from "../../types/venue.ts"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import {
  ChevronLeft,
  ChevronRight,
  X,
  FileText,
  ImageIcon,
  Camera,
  AlertTriangle,
  Check,
  MapPin,
  DollarSign,
  Users,
  Star,
} from "lucide-react"
import { validatePanoramaFile, getRecommendedSpecs } from "../../lib/panoramaValidation.ts"
import ChecklistEditor from "./ChecklistEditor.tsx"

interface VenueFormProps {
  initialData?: Partial<VenueFormData>
  onSubmit?: (data: VenueFormData) => Promise<void>
  onCancel?: () => void
  isLoading?: boolean
}

interface FormStep {
  id: number
  title: string
  description: string
  icon: React.ReactNode
}

const FORM_STEPS: FormStep[] = [
  {
    id: 1,
    title: "The Basics",
    description: "Tell us about your venue",
    icon: <MapPin className="w-5 h-5" />,
  },
  {
    id: 2,
    title: "Details & Amenities",
    description: "Capacity, pricing, and features",
    icon: <Users className="w-5 h-5" />,
  },
  {
    id: 3,
    title: "Media",
    description: "Photos and documents",
    icon: <ImageIcon className="w-5 h-5" />,
  },
  {
    id: 4,
    title: "Checklist",
    description: "Move-out checklist for renters",
    icon: <Check className="w-5 h-5" />,
  },
]

const AMENITY_OPTIONS = [
  { id: "wifi", label: "WiFi", icon: "📶" },
  { id: "parking", label: "Parking", icon: "🚗" },
  { id: "kitchen", label: "Kitchen", icon: "🍳" },
  { id: "bar", label: "Bar", icon: "🍸" },
  { id: "sound", label: "Sound System", icon: "🔊" },
  { id: "projector", label: "Projector", icon: "📽️" },
  { id: "ac", label: "Air Conditioning", icon: "❄️" },
  { id: "heating", label: "Heating", icon: "🔥" },
  { id: "wheelchair", label: "Wheelchair Access", icon: "♿" },
  { id: "garden", label: "Garden", icon: "🌿" },
  { id: "terrace", label: "Terrace", icon: "🏞️" },
  { id: "cctv", label: "CCTV", icon: "📹" },
  { id: "security", label: "Security Guard", icon: "👮" },
  { id: "catering", label: "Catering Available", icon: "🍽️" },
]

export default function VenueForm({ initialData, onSubmit, onCancel, isLoading = false }: VenueFormProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [errors, setErrors] = useState<VenueValidationErrors>({})

  // Debug component hydration
  useEffect(() => {
    console.log('=== VENUE FORM COMPONENT HYDRATED ===');
    console.log('onSubmit prop provided:', !!onSubmit);
    console.log('onCancel prop provided:', !!onCancel);
    console.log('Global handler available:', typeof window !== 'undefined' && 'handleVenueSubmission' in window);

    // Add a test function to window for debugging
    if (typeof window !== 'undefined') {
      (window as typeof window & { testFormSubmit: () => void }).testFormSubmit = () => {
        console.log('Testing form submission...');
        handleSubmit();
      };
    }
  }, [onSubmit, onCancel]);

  // Form data state
  const [formData, setFormData] = useState<VenueFormData>({
    title: initialData?.title || "",
    description: initialData?.description || "",
    address: initialData?.address || {
      street: "",
      city: "",
      country: "Ghana",
      state: "",
      postal_code: "",
    },
    capacity: initialData?.capacity || 1, // Default to 1 instead of 0 to avoid validation issues
    price_per_hour: initialData?.price_per_hour || 1, // Default to 1 instead of 0 to avoid validation issues
    amenities: initialData?.amenities || [],
    standard_photos: initialData?.standard_photos || [],
    pano_photo: initialData?.pano_photo || undefined,
    rental_agreement_pdf: initialData?.rental_agreement_pdf || undefined,
    is_published: initialData?.is_published || false,
    move_out_checklist: initialData?.move_out_checklist || [],
  })

  const updateFormData = useCallback((updates: Partial<VenueFormData>) => {
    setFormData((prev: VenueFormData) => ({ ...prev, ...updates }))
    // Clear related errors when user starts typing
    setErrors((prev: VenueValidationErrors) => {
      const newErrors = { ...prev }
      Object.keys(updates).forEach((key) => {
        delete newErrors[key as keyof VenueValidationErrors]
      })
      return newErrors
    })
  }, [])

  const updateAddress = useCallback((updates: Partial<VenueAddress>) => {
    setFormData((prev: VenueFormData) => ({
      ...prev,
      address: { ...prev.address, ...updates },
    }))
    setErrors((prev: VenueValidationErrors) => ({ ...prev, address: undefined }))
  }, [])

  const validateStep = (step: number): boolean => {
    const newErrors: VenueValidationErrors = {}

    if (step === 1) {
      if (!formData.title.trim()) newErrors.title = "Title is required"
      if (!formData.description.trim()) newErrors.description = "Description is required"
      if (!formData.address.street.trim()) newErrors.address = "Street address is required"
      if (!formData.address.city.trim()) newErrors.address = "City is required"
      if (!formData.address.country.trim()) newErrors.address = "Country is required"
    }

    if (step === 2) {
      if (formData.capacity <= 0) newErrors.capacity = "Capacity must be greater than 0"
      if (formData.price_per_hour <= 0) newErrors.price_per_hour = "Price must be greater than 0"
    }

    // Steps 3 and 4 don't have required validation - they are optional
    // Step 3: Media uploads are optional
    // Step 4: Checklist is optional

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep((prev) => Math.min(prev + 1, FORM_STEPS.length))
    }
  }

  const handlePrevious = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1))
  }

  const handleSubmit = async () => {
    console.log('=== FORM SUBMIT HANDLER CALLED ===');
    console.log('Current step:', currentStep);
    console.log('Form data:', formData);

    if (validateStep(currentStep)) {
      try {
        // Try to use the provided onSubmit prop first
        if (onSubmit) {
          console.log('Using provided onSubmit prop');
          await onSubmit(formData);
        } else {
          console.log('No onSubmit prop, using global handler');
          // Fallback to global handler
          if (typeof window !== 'undefined' && 'handleVenueSubmission' in window) {
            await (window as typeof window & { handleVenueSubmission: (data: VenueFormData) => Promise<void> }).handleVenueSubmission(formData);
          } else {
            console.error('No venue submission handler available');
            setErrors({ general: "Venue submission handler not available. Please refresh the page and try again." });
          }
        }
      } catch (error) {
        console.error('Submit handler error:', error);
        setErrors({ general: "Failed to save venue. Please try again." });
      }
    } else {
      console.log('Form validation failed for step:', currentStep);
    }
  }

  const toggleAmenity = (amenity: string) => {
    const newAmenities = formData.amenities.includes(amenity)
      ? formData.amenities.filter((a: string) => a !== amenity)
      : [...formData.amenities, amenity]
    updateFormData({ amenities: newAmenities })
  }

  const handleFileChange = async (
    field: "standard_photos" | "pano_photo" | "rental_agreement_pdf",
    files: FileList | null,
  ) => {
    if (!files) return

    if (field === "standard_photos") {
      const newFiles = Array.from(files)
      updateFormData({ standard_photos: [...formData.standard_photos, ...newFiles] })
    } else if (field === "pano_photo") {
      const file = files[0]

      // Validate 360° photo
      try {
        const validationResult = await validatePanoramaFile(file)

        if (!validationResult.isValid) {
          setErrors((prev: VenueValidationErrors) => ({
            ...prev,
            pano_photo: `Invalid 360° photo: ${validationResult.errors.join(", ")}`,
          }))
          return
        }

        if (validationResult.warnings.length > 0) {
          console.warn("360° photo warnings:", validationResult.warnings)
        }

        updateFormData({ [field]: file })
      } catch (_error) {
        setErrors((prev: VenueValidationErrors) => ({
          ...prev,
          pano_photo: "Failed to validate 360° photo",
        }))
      }
    } else {
      updateFormData({ [field]: files[0] })
    }
  }

  const removePhoto = (index: number) => {
    const newPhotos = formData.standard_photos.filter((_: File, i: number) => i !== index)
    updateFormData({ standard_photos: newPhotos })
  }

  const renderProgressBar = () => (
    <div className="mb-8 lg:mb-12">
      {/* Desktop Progress Bar */}
      <div className="hidden md:block">
        <div className="flex items-center justify-between mb-6">
          {FORM_STEPS.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div
                className={`
                relative w-12 h-12 rounded-2xl flex items-center justify-center text-sm font-bold transition-all duration-300
                ${
                  currentStep >= step.id
                    ? "bg-gradient-to-r from-emerald-500 to-emerald-600 text-white shadow-lg shadow-emerald-500/30"
                    : "bg-slate-100 text-slate-400 border-2 border-slate-200"
                }
              `}
              >
                {currentStep > step.id ? <Check className="w-5 h-5" /> : step.icon}
                {currentStep === step.id && (
                  <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-2xl blur opacity-30 animate-pulse"></div>
                )}
              </div>
              {index < FORM_STEPS.length - 1 && (
                <div
                  className={`
                  w-16 lg:w-24 h-1 mx-4 rounded-full transition-all duration-300
                  ${currentStep > step.id ? "bg-gradient-to-r from-emerald-500 to-emerald-600" : "bg-slate-200"}
                `}
                />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Mobile Progress Bar */}
      <div className="md:hidden mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="text-sm font-medium text-slate-600">
            Step {currentStep} of {FORM_STEPS.length}
          </div>
          <div className="text-sm font-medium text-emerald-600">
            {Math.round((currentStep / FORM_STEPS.length) * 100)}%
          </div>
        </div>
        <div className="w-full bg-slate-200 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-emerald-500 to-emerald-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(currentStep / FORM_STEPS.length) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Step Title */}
      <div className="text-center">
        <h2
          className="text-2xl md:text-3xl lg:text-4xl font-bold mb-3 text-slate-900"
          style={{ fontFamily: "'Poppins', 'Helvetica Neue', Arial, sans-serif" }}
        >
          {FORM_STEPS[currentStep - 1].title}
        </h2>
        <p
          className="text-base md:text-lg text-slate-600 max-w-md mx-auto"
          style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
        >
          {FORM_STEPS[currentStep - 1].description}
        </p>
      </div>
    </div>
  )

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-slate-50 to-white relative overflow-hidden"
      style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
    >
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-30 pointer-events-none">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgba(5, 150, 105, 0.1) 1px, transparent 0)`,
            backgroundSize: "40px 40px",
          }}
        />
      </div>

      {/* Floating Gradient Orbs */}
      <div
        className="absolute top-20 right-20 w-64 h-64 opacity-10"
        style={{
          background: "linear-gradient(135deg, rgba(5, 150, 105, 0.3) 0%, rgba(16, 185, 129, 0.2) 100%)",
          borderRadius: "50%",
          filter: "blur(60px)",
        }}
      ></div>
      <div
        className="absolute bottom-20 left-20 w-96 h-96 opacity-10"
        style={{
          background: "linear-gradient(135deg, rgba(245, 158, 11, 0.3) 0%, rgba(251, 191, 36, 0.2) 100%)",
          borderRadius: "50%",
          filter: "blur(80px)",
        }}
      ></div>

      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-12">
        <div className="bg-white rounded-3xl shadow-2xl border border-slate-200 overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-emerald-600 to-emerald-500 px-6 lg:px-8 py-6 lg:py-8">
            <div className="flex items-center justify-between">
              <div>
                <h1
                  className="text-2xl lg:text-3xl font-bold text-white"
                  style={{ fontFamily: "'Poppins', 'Helvetica Neue', Arial, sans-serif" }}
                >
                  {initialData ? "Edit Venue" : "Add New Venue"}
                </h1>
                <p className="text-emerald-100 mt-1">Create an amazing listing for your space</p>
              </div>
              <div className="hidden sm:flex items-center space-x-2 text-emerald-100">
                <Star className="w-5 h-5" />
                <span className="text-sm font-medium">Premium Listing</span>
              </div>
            </div>
          </div>

          {/* Form Content */}
          <div className="px-6 lg:px-8 py-8 lg:py-12">
            {renderProgressBar()}

            {/* Debug Info - Development Only */}
            {typeof window !== 'undefined' && globalThis.location?.hostname === 'localhost' && (
              <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h3 className="text-sm font-medium text-blue-800 mb-2">🔧 Debug Info</h3>
                <div className="text-xs text-blue-600 space-y-1">
                  <p>Component hydrated: ✅</p>
                  <p>onSubmit prop: {onSubmit ? '✅' : '❌'}</p>
                  <p>Global handler: {typeof window !== 'undefined' && 'handleVenueSubmission' in window ? '✅' : '❌'}</p>
                  <p>Current step: {currentStep}</p>
                </div>
                <button
                  type="button"
                  onClick={() => {
                    console.log('🔧 Debug button clicked - testing submit handler');
                    handleSubmit();
                  }}
                  className="mt-2 px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
                >
                  🧪 Test Submit Handler
                </button>
              </div>
            )}

            {errors.general && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-2xl animate-in fade-in duration-300">
                <div className="flex items-center">
                  <AlertTriangle className="w-5 h-5 text-red-500 mr-3" />
                  <p className="text-sm text-red-600 font-medium">{errors.general}</p>
                </div>
              </div>
            )}

            <form onSubmit={(e) => e.preventDefault()}>
              {/* Step 1: The Basics */}
              {currentStep === 1 && (
                <div className="space-y-6 lg:space-y-8 animate-in fade-in slide-in-from-right duration-300">
                  <div>
                    <label
                      htmlFor="title"
                      className="block text-sm font-semibold mb-3 text-slate-700"
                      style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                    >
                      Venue Title *
                    </label>
                    <Input
                      type="text"
                      id="title"
                      value={formData.title}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateFormData({ title: e.target.value })}
                      className={`w-full px-4 py-3 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-emerald-500/20 placeholder:text-slate-400 ${
                        errors.title
                          ? "border-red-300 focus:border-red-500"
                          : "border-slate-300 focus:border-emerald-500"
                      }`}
                      placeholder="e.g., Beautiful Garden Venue with City Views"
                      style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                    />
                    {errors.title && (
                      <p className="mt-2 text-sm text-red-600 flex items-center">
                        <AlertTriangle className="w-4 h-4 mr-1" />
                        {errors.title}
                      </p>
                    )}
                  </div>

                  <div>
                    <label
                      htmlFor="description"
                      className="block text-sm font-semibold mb-3 text-slate-700"
                      style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                    >
                      Description *
                    </label>
                    <Textarea
                      id="description"
                      rows={5}
                      value={formData.description}
                      onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => updateFormData({ description: e.target.value })}
                      className={`w-full px-4 py-3 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-emerald-500/20 resize-none placeholder:text-slate-400 ${
                        errors.description
                          ? "border-red-300 focus:border-red-500"
                          : "border-slate-300 focus:border-emerald-500"
                      }`}
                      placeholder="Describe your venue's atmosphere, unique features, and what makes it special for events..."
                      style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                    />
                    {errors.description && (
                      <p className="mt-2 text-sm text-red-600 flex items-center">
                        <AlertTriangle className="w-4 h-4 mr-1" />
                        {errors.description}
                      </p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 lg:gap-6">
                    <div className="md:col-span-2">
                      <label
                        htmlFor="street"
                        className="block text-sm font-semibold mb-3 text-slate-700"
                        style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                      >
                        Street Address *
                      </label>
                      <Input
                        type="text"
                        id="street"
                        value={formData.address.street}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateAddress({ street: e.target.value })}
                        className={`w-full px-4 py-3 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-emerald-500/20 placeholder:text-slate-400 ${
                          errors.address
                            ? "border-red-300 focus:border-red-500"
                            : "border-slate-300 focus:border-emerald-500"
                        }`}
                        placeholder="e.g., 123 Liberation Road"
                        style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                      />
                    </div>

                    <div>
                      <label
                        htmlFor="city"
                        className="block text-sm font-semibold mb-3 text-slate-700"
                        style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                      >
                        City *
                      </label>
                      <Input
                        type="text"
                        id="city"
                        value={formData.address.city}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateAddress({ city: e.target.value })}
                        className={`w-full px-4 py-3 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-emerald-500/20 placeholder:text-slate-400 ${
                          errors.address
                            ? "border-red-300 focus:border-red-500"
                            : "border-slate-300 focus:border-emerald-500"
                        }`}
                        placeholder="e.g., Accra, Kumasi, Tamale"
                        style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                      />
                    </div>

                    <div>
                      <label
                        htmlFor="country"
                        className="block text-sm font-semibold mb-3 text-slate-700"
                        style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                      >
                        Country *
                      </label>
                      <Input
                        type="text"
                        id="country"
                        value="Ghana"
                        readOnly
                        className="w-full px-4 py-3 text-base border-2 border-slate-200 bg-slate-50 text-slate-600 rounded-xl cursor-not-allowed"
                        style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                      />
                    </div>

                    <div>
                      <label
                        htmlFor="postal"
                        className="block text-sm font-semibold mb-3 text-slate-700"
                        style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                      >
                        Postal Code
                      </label>
                      <Input
                        type="text"
                        id="postal"
                        value={formData.address.postal_code || ""}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateAddress({ postal_code: e.target.value })}
                        className="w-full px-4 py-3 text-base border-2 border-slate-300 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-emerald-500/20 focus:border-emerald-500 placeholder:text-slate-400"
                        placeholder="e.g., GA-123-4567 (Ghana Post GPS)"
                        style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                      />
                    </div>
                  </div>

                  {errors.address && (
                    <p className="text-sm text-red-600 flex items-center">
                      <AlertTriangle className="w-4 h-4 mr-1" />
                      {errors.address}
                    </p>
                  )}
                </div>
              )}

              {/* Step 2: Details & Amenities */}
              {currentStep === 2 && (
                <div className="space-y-6 lg:space-y-8 animate-in fade-in slide-in-from-right duration-300">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 lg:gap-8">
                    <div>
                      <label
                        htmlFor="capacity"
                        className="block text-sm font-semibold mb-3 text-slate-700"
                        style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                      >
                        Maximum Capacity *
                      </label>
                      <div className="relative">
                        <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
                        <Input
                          type="number"
                          id="capacity"
                          min="1"
                          value={formData.capacity || ""}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateFormData({ capacity: Number.parseInt(e.target.value) || 0 })}
                          className={`w-full pl-12 pr-4 py-3 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-emerald-500/20 placeholder:text-slate-400 ${
                            errors.capacity
                              ? "border-red-300 focus:border-red-500"
                              : "border-slate-300 focus:border-emerald-500"
                          }`}
                          placeholder="e.g., 100"
                          style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                        />
                      </div>
                      {errors.capacity && (
                        <p className="mt-2 text-sm text-red-600 flex items-center">
                          <AlertTriangle className="w-4 h-4 mr-1" />
                          {errors.capacity}
                        </p>
                      )}
                    </div>

                    <div>
                      <label
                        htmlFor="price"
                        className="block text-sm font-semibold mb-3 text-slate-700"
                        style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                      >
                        Price per Hour *
                      </label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
                        <Input
                          type="number"
                          id="price"
                          min="0"
                          step="0.01"
                          value={formData.price_per_hour || ""}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateFormData({ price_per_hour: Number.parseFloat(e.target.value) || 0 })}
                          className={`w-full pl-12 pr-4 py-3 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-emerald-500/20 placeholder:text-slate-400 ${
                            errors.price_per_hour
                              ? "border-red-300 focus:border-red-500"
                              : "border-slate-300 focus:border-emerald-500"
                          }`}
                          placeholder="0.00"
                          style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                        />
                      </div>
                      {errors.price_per_hour && (
                        <p className="mt-2 text-sm text-red-600 flex items-center">
                          <AlertTriangle className="w-4 h-4 mr-1" />
                          {errors.price_per_hour}
                        </p>
                      )}
                    </div>
                  </div>

                  <div>
                    <label
                      className="block text-sm font-semibold mb-4 text-slate-700"
                      style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                    >
                      Amenities & Features
                    </label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                      {AMENITY_OPTIONS.map((amenity) => (
                        <label
                          key={amenity.id}
                          className={`
                            flex items-center p-4 border-2 rounded-2xl cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-md
                            ${
                              formData.amenities.includes(amenity.label)
                                ? "border-emerald-500 bg-emerald-50 shadow-lg shadow-emerald-500/20"
                                : "border-slate-200 bg-white hover:border-slate-300 hover:bg-slate-50"
                            }
                          `}
                        >
                          <Checkbox
                            checked={formData.amenities.includes(amenity.label)}
                            onCheckedChange={() => toggleAmenity(amenity.label)}
                            className="data-[state=checked]:bg-emerald-600 data-[state=checked]:border-emerald-600"
                          />
                          <span className="text-xl mr-3">{amenity.icon}</span>
                          <span
                            className="text-sm font-medium text-slate-700"
                            style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                          >
                            {amenity.label}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Step 3: Media */}
              {currentStep === 3 && (
                <div className="space-y-6 lg:space-y-8 animate-in fade-in slide-in-from-right duration-300">
                  {/* Standard Photos */}
                  <div>
                    <label
                      className="block text-sm font-semibold mb-4 text-slate-700"
                      style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                    >
                      Venue Photos
                    </label>
                    <div className="border-2 border-dashed border-slate-300 rounded-2xl p-8 text-center hover:border-emerald-500 hover:bg-emerald-50/50 transition-all duration-300 group">
                      <input
                        type="file"
                        multiple
                        accept="image/*"
                        onChange={(e) => handleFileChange("standard_photos", e.target.files)}
                        className="hidden"
                        id="standard-photos"
                      />
                      <label htmlFor="standard-photos" className="cursor-pointer">
                        <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-slate-100 flex items-center justify-center group-hover:bg-emerald-100 transition-colors duration-300">
                          <ImageIcon className="w-8 h-8 text-slate-400 group-hover:text-emerald-600" />
                        </div>
                        <p className="text-base font-medium text-slate-700 mb-2">Click to upload venue photos</p>
                        <p className="text-sm text-slate-500">PNG, JPG up to 10MB each • Multiple files supported</p>
                      </label>
                    </div>

                    {formData.standard_photos.length > 0 && (
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-6">
                        {formData.standard_photos.map((file: File, index: number) => (
                          <div key={index} className="relative group">
                            <img
                              src={URL.createObjectURL(file) || "/placeholder.svg"}
                              alt={`Preview ${index + 1}`}
                              className="w-full h-24 object-cover rounded-xl border-2 border-slate-200"
                            />
                            <button
                              type="button"
                              onClick={() => removePhoto(index)}
                              className="absolute -top-2 -right-2 w-7 h-7 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 hover:scale-110 shadow-lg"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* 360° Photo */}
                  <div>
                    <label
                      className="block text-sm font-semibold mb-4 text-slate-700"
                      style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                    >
                      360° Photo (Optional)
                    </label>
                    <div className="border-2 border-dashed border-slate-300 rounded-2xl p-8 text-center hover:border-emerald-500 hover:bg-emerald-50/50 transition-all duration-300 group">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => handleFileChange("pano_photo", e.target.files)}
                        className="hidden"
                        id="pano-photo"
                      />
                      <label htmlFor="pano-photo" className="cursor-pointer">
                        <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-slate-100 flex items-center justify-center group-hover:bg-emerald-100 transition-colors duration-300">
                          <Camera className="w-8 h-8 text-slate-400 group-hover:text-emerald-600" />
                        </div>
                        <p className="text-base font-medium text-slate-700 mb-2">Upload 360° panoramic photo</p>
                        <p className="text-sm text-slate-500">Equirectangular format, 2:1 aspect ratio</p>
                        <p className="text-xs text-slate-400 mt-1">Recommended: 4096×2048 to 8192×4096 pixels</p>
                      </label>
                    </div>

                    {errors.pano_photo && (
                      <div className="mt-4 p-4 bg-red-50 border-2 border-red-200 rounded-2xl">
                        <div className="flex items-start">
                          <AlertTriangle className="w-5 h-5 text-red-500 mr-3 mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="text-sm font-medium text-red-600 mb-2">{errors.pano_photo}</p>
                            <details className="mt-2">
                              <summary className="text-xs text-red-500 cursor-pointer font-medium">
                                View requirements
                              </summary>
                              <div className="mt-2 text-xs text-red-600 whitespace-pre-line bg-red-100 p-3 rounded-lg">
                                {getRecommendedSpecs()}
                              </div>
                            </details>
                          </div>
                        </div>
                      </div>
                    )}

                    {formData.pano_photo && (
                      <div className="mt-4 p-4 bg-emerald-50 border-2 border-emerald-200 rounded-2xl flex items-center justify-between">
                        <div className="flex items-center">
                          <Camera className="w-5 h-5 text-emerald-600 mr-3" />
                          <span className="text-sm font-medium text-emerald-700">{formData.pano_photo.name}</span>
                        </div>
                        <button
                          type="button"
                          onClick={() => updateFormData({ pano_photo: undefined })}
                          className="text-red-500 hover:text-red-700 p-1 rounded-lg hover:bg-red-100 transition-colors duration-300"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    )}
                  </div>

                  {/* Rental Agreement PDF */}
                  <div>
                    <label
                      className="block text-sm font-semibold mb-4 text-slate-700"
                      style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                    >
                      Rental Agreement (Optional)
                    </label>
                    <div className="border-2 border-dashed border-slate-300 rounded-2xl p-8 text-center hover:border-emerald-500 hover:bg-emerald-50/50 transition-all duration-300 group">
                      <input
                        type="file"
                        accept=".pdf"
                        onChange={(e) => handleFileChange("rental_agreement_pdf", e.target.files)}
                        className="hidden"
                        id="rental-agreement"
                      />
                      <label htmlFor="rental-agreement" className="cursor-pointer">
                        <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-slate-100 flex items-center justify-center group-hover:bg-emerald-100 transition-colors duration-300">
                          <FileText className="w-8 h-8 text-slate-400 group-hover:text-emerald-600" />
                        </div>
                        <p className="text-base font-medium text-slate-700 mb-2">Upload rental agreement</p>
                        <p className="text-sm text-slate-500">PDF format only • Max 10MB</p>
                      </label>
                    </div>

                    {formData.rental_agreement_pdf && (
                      <div className="mt-4 p-4 bg-blue-50 border-2 border-blue-200 rounded-2xl flex items-center justify-between">
                        <div className="flex items-center">
                          <FileText className="w-5 h-5 text-blue-600 mr-3" />
                          <span className="text-sm font-medium text-blue-700">
                            {formData.rental_agreement_pdf.name}
                          </span>
                        </div>
                        <button
                          type="button"
                          onClick={() => updateFormData({ rental_agreement_pdf: undefined })}
                          className="text-red-500 hover:text-red-700 p-1 rounded-lg hover:bg-red-100 transition-colors duration-300"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    )}
                  </div>

                  {/* Publish Option */}
                  <div className="border-t-2 border-slate-200 pt-6">
                    <label className="flex items-center p-4 bg-gradient-to-r from-emerald-50 to-emerald-100 rounded-2xl border-2 border-emerald-200 cursor-pointer hover:from-emerald-100 hover:to-emerald-200 transition-all duration-300">
                      <Checkbox
                        checked={formData.is_published}
                        onCheckedChange={(checked: boolean) => updateFormData({ is_published: checked })}
                        className="data-[state=checked]:bg-emerald-600 data-[state=checked]:border-emerald-600"
                      />
                      <div className="ml-4">
                        <span
                          className="text-sm font-semibold text-emerald-800"
                          style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                        >
                          Publish venue immediately
                        </span>
                        <p className="text-xs text-emerald-600 mt-1">
                          Your venue will be visible to potential renters right away
                        </p>
                      </div>
                    </label>
                  </div>
                </div>
              )}

              {/* Step 4: Checklist */}
              {currentStep === 4 && (
                <div className="space-y-6 lg:space-y-8 animate-in fade-in slide-in-from-right duration-300">
                  <ChecklistEditor
                    initialItems={formData.move_out_checklist || []}
                    onChange={(items: ChecklistItem[]) => updateFormData({ move_out_checklist: items })}
                    disabled={isLoading}
                  />
                </div>
              )}

              {/* Navigation */}
              <div className="flex flex-col sm:flex-row justify-between items-center mt-12 pt-8 border-t-2 border-slate-200 gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading}
                  className="w-full sm:w-auto px-6 py-3 border-2 border-slate-300 text-slate-700 hover:border-slate-400 hover:bg-slate-50 rounded-xl font-semibold transition-all duration-300"
                >
                  Cancel
                </Button>

                <div className="flex gap-3 w-full sm:w-auto">
                  {currentStep > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handlePrevious}
                      disabled={isLoading}
                      className="flex-1 sm:flex-none px-6 py-3 border-2 border-emerald-300 text-emerald-700 hover:border-emerald-400 hover:bg-emerald-50 rounded-xl font-semibold transition-all duration-300"
                    >
                      <ChevronLeft className="w-4 h-4 mr-2" />
                      Previous
                    </Button>
                  )}

                  {currentStep < FORM_STEPS.length ? (
                    <Button
                      type="button"
                      onClick={handleNext}
                      disabled={isLoading}
                      className="flex-1 sm:flex-none px-6 py-3 bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white rounded-xl font-bold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl"
                    >
                      Next
                      <ChevronRight className="w-4 h-4 ml-2" />
                    </Button>
                  ) : (
                    <Button
                      type="button"
                      onClick={handleSubmit}
                      disabled={isLoading}
                      className="flex-1 sm:flex-none px-8 py-3 bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-black rounded-xl font-bold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl"
                    >
                      {isLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-2 border-black border-t-transparent mr-2"></div>
                          Publishing...
                        </>
                      ) : (
                        <>
                          <Star className="w-4 h-4 mr-2" />
                          Publish Venue
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}
