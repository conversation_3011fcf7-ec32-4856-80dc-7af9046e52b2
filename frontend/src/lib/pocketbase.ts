import PocketBase, { type RecordModel } from 'pocketbase';
import type { VenueFormData } from '../types/venue.ts';
import type { Message } from '../types/message.ts';
import type { Booking } from '../types/booking.ts';
import type {
  Review,
  ReviewFormData,
  ReviewListResponse,
  OwnerResponseData,
  ReviewPrompt,
  CreateReviewResponse,
  GetReviewsResponse,
  UpdateReviewResponse,
  GetReviewStatsResponse,
  GetReviewPromptsResponse
} from '../types/review.ts';

// Get PocketBase URL from environment variables
// Use PUBLIC_ prefixed variable for client-side, fallback to server-side variable
const POCKETBASE_URL = import.meta.env.PUBLIC_POCKETBASE_URL || import.meta.env.POCKETBASE_URL || 'https://trodoorentals.pockethost.io';

// console.log('PocketBase URL:', POCKETBASE_URL);

// Create singleton PocketBase client instance
let _pb: PocketBase | null = null;

export function getPocketBase(): PocketBase {
  if (!_pb) {
    _pb = new PocketBase(POCKETBASE_URL);
    
    // Enable auto cancellation for duplicate requests
    _pb.autoCancellation(false);

    // Configure default settings
    _pb.beforeSend = function (url, options) {
      // Add any default headers or configurations here
      return { url, options };
    };
  }

  return _pb;
}

// Export the singleton instance
export const pocketbase = getPocketBase();
export const pb = pocketbase; // Alias for convenience

// Helper functions for common operations
export async function authenticateUser(email: string, password: string): Promise<{
  success: boolean;
  user?: RecordModel;
  token?: string;
  error?: string;
}> {
  try {
    console.log('Attempting to authenticate user:', email);
    const authData = await pocketbase.collection('users').authWithPassword(email, password);
    console.log('Authentication successful:', authData.record.id);
    return { success: true, user: authData.record, token: authData.token };
  } catch (error) {
    console.error('Authentication failed:', error);

    // Provide more specific error messages
    let errorMessage = 'Authentication failed';
    if (error instanceof Error) {
      if (error.message.includes('Failed to authenticate')) {
        errorMessage = 'Invalid email or password. Please check your credentials and try again.';
      } else if (error.message.includes('verification')) {
        errorMessage = 'Please verify your email address before signing in.';
      } else {
        errorMessage = error.message;
      }
    }

    return { success: false, error: errorMessage };
  }
}

export async function registerUser(email: string, password: string, passwordConfirm: string, name?: string): Promise<{
  success: boolean;
  user?: RecordModel;
  error?: string;
}> {
  try {
    const userData = {
      email,
      password,
      passwordConfirm,
      name: name || email.split('@')[0], // Use email prefix as default name
      is_active: true,
      roles: ['renter'] // Default role for new users
    };

    console.log('Attempting to register user:', { email, name: userData.name });

    const user = await pocketbase.collection('users').create(userData);
    console.log('User created successfully:', user.id);

    // Send verification email (optional, don't fail if this fails)
    try {
      await pocketbase.collection('users').requestVerification(email);
      console.log('Verification email sent');
    } catch (verificationError) {
      console.warn('Failed to send verification email:', verificationError);
      // Don't fail the registration if verification email fails
    }

    return { success: true, user };
  } catch (error) {
    console.error('Registration failed:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Registration failed' };
  }
}

export function logout() {
  pocketbase.authStore.clear();
}

export function getCurrentUser() {
  return pocketbase.authStore.model;
}

export function isAuthenticated(): boolean {
  return pocketbase.authStore.isValid;
}


// Make test function available globally for debugging
if (typeof window !== 'undefined') {
  (window as typeof window & { testVenueCreation: typeof testVenueCreation }).testVenueCreation = testVenueCreation;
}

// Venue-related operations
export async function createVenue(venueData: VenueFormData): Promise<{
  success: boolean;
  venue?: RecordModel;
  error?: string;
}> {
  try {
    console.log('Creating venue:', venueData.title);
    console.log('Venue data:', venueData);

    // Check if user is authenticated
    const currentUser = pocketbase.authStore.model;
    const authToken = pocketbase.authStore.token;

    console.log('Auth check - Token exists:', !!authToken);
    console.log('Auth check - User model exists:', !!currentUser);
    console.log('Auth check - Is valid:', pocketbase.authStore.isValid);

    if (!currentUser || !authToken || !pocketbase.authStore.isValid) {
      console.error('User not authenticated - authStore.model:', !!currentUser, 'token:', !!authToken, 'isValid:', pocketbase.authStore.isValid);
      return { success: false, error: 'User not authenticated. Please log in and try again.' };
    }

    console.log('User authenticated:', currentUser.id, 'Email:', currentUser.email);
    console.log('User roles:', currentUser.roles);

    // Validate required fields
    if (!venueData.title?.trim()) {
      return { success: false, error: 'Title is required' };
    }
    if (!venueData.description?.trim()) {
      return { success: false, error: 'Description is required' };
    }
    if (!venueData.address?.street?.trim()) {
      return { success: false, error: 'Street address is required' };
    }
    if (!venueData.address?.city?.trim()) {
      return { success: false, error: 'City is required' };
    }
    if (!venueData.capacity || venueData.capacity <= 0) {
      return { success: false, error: 'Capacity must be greater than 0' };
    }
    if (!venueData.price_per_hour || venueData.price_per_hour <= 0) {
      return { success: false, error: 'Price per hour must be greater than 0' };
    }

    // Create FormData for file uploads
    const formData = new FormData();

    // Add basic venue data
    formData.append('title', venueData.title.trim());

    // Convert description to HTML format for PocketBase editor field
    const description = venueData.description.trim();
    const htmlDescription = description.startsWith('<') ? description : `<p>${description}</p>`;
    formData.append('description', htmlDescription);

    formData.append('address', JSON.stringify(venueData.address));
    formData.append('capacity', venueData.capacity.toString());
    formData.append('price_per_hour', venueData.price_per_hour.toString());
    formData.append('amenities', JSON.stringify(venueData.amenities || []));
    formData.append('is_published', venueData.is_published.toString());
    formData.append('owner', currentUser.id);

    // Add move-out checklist
    if (venueData.move_out_checklist && venueData.move_out_checklist.length > 0) {
      // Transform checklist items to match backend schema (remove 'completed' field)
      const backendChecklist = venueData.move_out_checklist.map(item => ({
        id: item.id,
        text: item.text
      }));
      console.log(`Adding ${venueData.move_out_checklist.length} checklist items:`, backendChecklist);
      formData.append('move_out_checklist', JSON.stringify(backendChecklist));
    } else {
      console.log('No checklist items to add, using empty array');
      formData.append('move_out_checklist', JSON.stringify([]));
    }

    // Add standard photos
    if (venueData.standard_photos && venueData.standard_photos.length > 0) {
      console.log(`Adding ${venueData.standard_photos.length} standard photos`);
      venueData.standard_photos.forEach((file: File, index) => {
        console.log(`Photo ${index}: ${file.name}, ${file.size} bytes, ${file.type}`);
        formData.append('standard_photos', file);
      });
    } else {
      console.log('No standard photos to upload');
    }

    // Add panoramic photo
    if (venueData.pano_photo) {
      console.log(`Adding panorama photo: ${venueData.pano_photo.name}, ${venueData.pano_photo.size} bytes, ${venueData.pano_photo.type}`);
      formData.append('pano_photo', venueData.pano_photo);
    } else {
      console.log('No panorama photo to upload');
    }

    // Add rental agreement PDF
    if (venueData.rental_agreement_pdf) {
      console.log(`Adding rental agreement: ${venueData.rental_agreement_pdf.name}, ${venueData.rental_agreement_pdf.size} bytes, ${venueData.rental_agreement_pdf.type}`);
      formData.append('rental_agreement_pdf', venueData.rental_agreement_pdf);
    } else {
      console.log('No rental agreement PDF to upload');
    }

    console.log('Sending venue creation request...');

    // Log the form data that will be sent for debugging
    console.log('Form data being sent:');
    for (const [key, value] of formData.entries()) {
      if (value instanceof File) {
        console.log(`${key}: File(${value.name}, ${value.size} bytes, ${value.type})`);
      } else {
        console.log(`${key}: ${value}`);
      }
    }

    const venue = await pocketbase.collection('venues').create(formData);
    console.log('Venue created successfully:', venue.id);

    return { success: true, venue };
  } catch (error) {
    console.error('Failed to create venue:', error);
    console.error('Error details:', JSON.stringify(error, null, 2));

    // Extract more detailed error information
    let errorMessage = 'Failed to create venue';
    if (error instanceof Error) {
      errorMessage = error.message;
      console.error('Error stack:', error.stack);
    } else if (typeof error === 'object' && error !== null) {
      // Handle PocketBase specific errors
      const pbError = error as Record<string, unknown>;
      console.error('PocketBase error object:', pbError);

      if (pbError.data && typeof pbError.data === 'object') {
        const fieldErrors = Object.entries(pbError.data as Record<string, unknown>).map(([field, message]) => `${field}: ${message}`);
        errorMessage = `Validation errors: ${fieldErrors.join(', ')}`;
        console.error('Field validation errors:', pbError.data);
      } else if (pbError.message && typeof pbError.message === 'string') {
        errorMessage = pbError.message;
      } else if (pbError.status) {
        errorMessage = `HTTP ${pbError.status}: ${pbError.statusText || 'Unknown error'}`;
      }
    }

    return {
      success: false,
      error: errorMessage
    };
  }
}

export async function updateVenue(venueId: string, venueData: Partial<VenueFormData>): Promise<{
  success: boolean;
  venue?: RecordModel;
  error?: string;
}> {
  try {
    console.log('Updating venue:', venueId);

    // Create FormData for file uploads
    const formData = new FormData();

    // Add basic venue data (only include fields that are being updated)
    if (venueData.title !== undefined) formData.append('title', venueData.title);
    if (venueData.description !== undefined) formData.append('description', venueData.description);
    if (venueData.address !== undefined) formData.append('address', JSON.stringify(venueData.address));
    if (venueData.capacity !== undefined) formData.append('capacity', venueData.capacity.toString());
    if (venueData.price_per_hour !== undefined) formData.append('price_per_hour', venueData.price_per_hour.toString());
    if (venueData.amenities !== undefined) formData.append('amenities', JSON.stringify(venueData.amenities));
    if (venueData.is_published !== undefined) formData.append('is_published', venueData.is_published.toString());

    // Add move-out checklist if provided
    if (venueData.move_out_checklist !== undefined) {
      // Transform checklist items to match backend schema (remove 'completed' field)
      const backendChecklist = venueData.move_out_checklist.map(item => ({
        id: item.id,
        text: item.text
      }));
      formData.append('move_out_checklist', JSON.stringify(backendChecklist));
    }

    // Add new files if provided
    if (venueData.standard_photos && venueData.standard_photos.length > 0) {
      venueData.standard_photos.forEach((file: File) => {
        formData.append('standard_photos', file);
      });
    }

    if (venueData.pano_photo) {
      formData.append('pano_photo', venueData.pano_photo);
    }

    if (venueData.rental_agreement_pdf) {
      formData.append('rental_agreement_pdf', venueData.rental_agreement_pdf);
    }

    const venue = await pocketbase.collection('venues').update(venueId, formData);
    console.log('Venue updated successfully:', venue.id);

    return { success: true, venue };
  } catch (error) {
    console.error('Failed to update venue:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update venue'
    };
  }
}

export async function getVenue(venueId: string, expand?: string): Promise<{
  success: boolean;
  venue?: RecordModel;
  error?: string;
}> {
  try {
    const options = expand ? { expand } : {};
    const venue = await pocketbase.collection('venues').getOne(venueId, options);
    return { success: true, venue };
  } catch (error) {
    console.error('Failed to get venue:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get venue'
    };
  }
}

export async function getVenues(page = 1, perPage = 20, filter?: string, sort?: string): Promise<{
  success: boolean;
  venues: RecordModel[];
  totalPages: number;
  totalItems: number;
  error?: string;
}> {
  try {
    const options: Record<string, unknown> = { page, perPage };
    if (filter) options.filter = filter;
    if (sort) options.sort = sort;

    const result = await pocketbase.collection('venues').getList(page, perPage, options);
    return { success: true, venues: result.items, totalPages: result.totalPages, totalItems: result.totalItems };
  } catch (error) {
    console.error('Failed to get venues:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get venues',
      venues: [],
      totalPages: 0,
      totalItems: 0
    };
  }
}

export async function getUserVenues(userId?: string, page = 1, perPage = 20): Promise<{
  success: boolean;
  venues: RecordModel[];
  totalPages: number;
  totalItems: number;
  error?: string;
}> {
  try {
    const ownerId = userId || pocketbase.authStore.model?.id;
    if (!ownerId) {
      return { success: false, error: 'User not authenticated', venues: [], totalPages: 0, totalItems: 0 };
    }

    const filter = `owner = "${ownerId}"`;
    const result = await pocketbase.collection('venues').getList(page, perPage, {
      filter,
      sort: '-created',
      expand: 'owner'
    });

    return { success: true, venues: result.items, totalPages: result.totalPages, totalItems: result.totalItems };
  } catch (error) {
    console.error('Failed to get user venues:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get user venues',
      venues: [],
      totalPages: 0,
      totalItems: 0
    };
  }
}

export async function deleteVenue(venueId: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    await pocketbase.collection('venues').delete(venueId);
    console.log('Venue deleted successfully:', venueId);
    return { success: true };
  } catch (error) {
    console.error('Failed to delete venue:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete venue'
    };
  }
}

export async function toggleVenuePublished(venueId: string, isPublished: boolean): Promise<{
  success: boolean;
  venue?: RecordModel;
  error?: string;
}> {
  try {
    const venue = await pocketbase.collection('venues').update(venueId, {
      is_published: isPublished
    });
    console.log('Venue publication status updated:', venueId, isPublished);
    return { success: true, venue };
  } catch (error) {
    console.error('Failed to update venue publication status:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update venue publication status'
    };
  }
}

// Booking-related operations
export async function createBooking(bookingData: {
  venue: string | null | undefined;
  owner: string | null | undefined;
  start_date: string;
  end_date: string;
  total_price: number;
  platform_fee: number;
  payout_amount: number;
  special_requests?: string;
}): Promise<{
  success: boolean;
  booking?: Booking;
  error?: string;
}> {
  try {
    const currentUser = pocketbase.authStore.model;
    if (!currentUser) {
      return { success: false, error: 'User not authenticated' };
    }

    if (!bookingData.venue || !bookingData.owner) {
      return { success: false, error: 'Venue and owner are required' };
    }

    // Check for date conflicts
    const conflictFilter = `venue = "${bookingData.venue}" && (start_date < "${bookingData.end_date}" && end_date > "${bookingData.start_date}") && (status = "paid" || status = "confirmed")`;
    const conflicts = await pocketbase.collection('bookings').getList(1, 1, {
      filter: conflictFilter
    });

    if (conflicts.totalItems > 0) {
      return { success: false, error: 'Selected dates are not available' };
    }

    const createdBooking = await pocketbase.collection('bookings').create({
      venue: bookingData.venue,
      owner: bookingData.owner,
      start_date: bookingData.start_date,
      end_date: bookingData.end_date,
      total_price: bookingData.total_price,
      platform_fee: bookingData.platform_fee,
      payout_amount: bookingData.payout_amount,
      special_requests: bookingData.special_requests || '',
      renter: currentUser.id,
      status: 'pending'
    });

    console.log('Booking created successfully:', createdBooking.id);

    // Get the booking with expanded relations
    const result = await getBooking(createdBooking.id);
    return result;
  } catch (error) {
    console.error('Failed to create booking:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create booking'
    };
  }
}

export async function getUserBookings(userId?: string, page = 1, perPage = 20, userRole: 'renter' | 'owner' = 'renter'): Promise<{
  success: boolean;
  bookings: Booking[];
  totalPages: number;
  totalItems: number;
  error?: string;
}> {
  try {
    const currentUserId = userId || pocketbase.authStore.model?.id;
    if (!currentUserId) {
      return { success: false, error: 'User not authenticated', bookings: [], totalPages: 0, totalItems: 0 };
    }

    const filter = userRole === 'renter'
      ? `renter = "${currentUserId}"`
      : `owner = "${currentUserId}"`;

    const result = await pocketbase.collection('bookings').getList(page, perPage, {
      filter,
      sort: '-created',
      expand: 'venue,venue.owner,renter,owner'
    });

    // Transform RecordModel[] to Booking[]
    const transformedBookings: Booking[] = result.items.map((item: RecordModel) => ({
      id: item.id,
      renter: {
        id: item.expand?.renter?.id || item.renter,
        name: item.expand?.renter?.name || '',
        email: item.expand?.renter?.email || '',
        avatar: item.expand?.renter?.avatar
      },
      owner: {
        id: item.expand?.owner?.id || item.owner,
        name: item.expand?.owner?.name || '',
        email: item.expand?.owner?.email || '',
        avatar: item.expand?.owner?.avatar
      },
      venue: {
        id: item.expand?.venue?.id || item.venue,
        title: item.expand?.venue?.title || '',
        address: item.expand?.venue?.address || '',
        owner: {
          id: item.expand?.venue?.expand?.owner?.id || item.expand?.venue?.owner || '',
          name: item.expand?.venue?.expand?.owner?.name || '',
          email: item.expand?.venue?.expand?.owner?.email || ''
        },
        average_rating: item.expand?.venue?.average_rating,
        review_count: item.expand?.venue?.review_count
      },
      start_date: item.start_date,
      end_date: item.end_date,
      total_price: item.total_price,
      platform_fee: item.platform_fee,
      payout_amount: item.payout_amount,
      status: item.status,
      paystack_ref: item.paystack_ref,
      special_requests: item.special_requests,
      created: item.created,
      updated: item.updated
    }));

    return {
      success: true,
      bookings: transformedBookings,
      totalPages: result.totalPages,
      totalItems: result.totalItems
    };
  } catch (error) {
    console.error('Failed to get user bookings:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get user bookings',
      bookings: [],
      totalPages: 0,
      totalItems: 0
    };
  }
}

export async function getBooking(bookingId: string): Promise<{
  success: boolean;
  booking?: Booking;
  error?: string;
}> {
  try {
    const result = await pocketbase.collection('bookings').getOne(bookingId, {
      expand: 'venue,venue.owner,renter,owner'
    });

    // Transform RecordModel to Booking type
    const booking: Booking = {
      id: result.id,
      renter: {
        id: result.expand?.renter?.id || result.renter,
        name: result.expand?.renter?.name || '',
        email: result.expand?.renter?.email || '',
        avatar: result.expand?.renter?.avatar
      },
      owner: {
        id: result.expand?.owner?.id || result.owner,
        name: result.expand?.owner?.name || '',
        email: result.expand?.owner?.email || '',
        avatar: result.expand?.owner?.avatar
      },
      venue: {
        id: result.expand?.venue?.id || result.venue,
        title: result.expand?.venue?.title || '',
        address: result.expand?.venue?.address || '',
        owner: {
          id: result.expand?.venue?.expand?.owner?.id || result.expand?.venue?.owner || '',
          name: result.expand?.venue?.expand?.owner?.name || '',
          email: result.expand?.venue?.expand?.owner?.email || ''
        },
        average_rating: result.expand?.venue?.average_rating,
        review_count: result.expand?.venue?.review_count
      },
      start_date: result.start_date,
      end_date: result.end_date,
      total_price: result.total_price,
      platform_fee: result.platform_fee,
      payout_amount: result.payout_amount,
      status: result.status,
      paystack_ref: result.paystack_ref,
      special_requests: result.special_requests,
      created: result.created,
      updated: result.updated
    };

    return { success: true, booking };
  } catch (error) {
    console.error('Failed to get booking:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get booking'
    };
  }
}

export async function updateBookingStatus(bookingId: string, status: string, paystack_ref?: string): Promise<{
  success: boolean;
  booking?: Booking;
  error?: string;
}> {
  try {
    const updateData: Record<string, string> = { status };
    if (paystack_ref) {
      updateData.paystack_ref = paystack_ref;
    }

    await pocketbase.collection('bookings').update(bookingId, updateData);
    console.log('Booking status updated:', bookingId, status);

    // Get the updated booking with expanded relations
    const result = await getBooking(bookingId);
    return result;
  } catch (error) {
    console.error('Failed to update booking status:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update booking status'
    };
  }
}

export async function approveBooking(bookingId: string) {
  return await updateBookingStatus(bookingId, 'confirmed');
}

export async function denyBooking(bookingId: string) {
  return await updateBookingStatus(bookingId, 'denied');
}

export async function cancelBooking(bookingId: string) {
  return await updateBookingStatus(bookingId, 'cancelled');
}

export async function checkDateAvailability(venueId: string, startDate: string, endDate: string, excludeBookingId?: string) {
  try {
    let filter = `venue = "${venueId}" && (start_date < "${endDate}" && end_date > "${startDate}") && (status = "paid" || status = "confirmed")`;

    if (excludeBookingId) {
      filter += ` && id != "${excludeBookingId}"`;
    }

    const conflicts = await pocketbase.collection('bookings').getList(1, 1, {
      filter
    });

    return {
      success: true,
      available: conflicts.totalItems === 0,
      conflictCount: conflicts.totalItems
    };
  } catch (error) {
    console.error('Failed to check date availability:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to check availability',
      available: false
    };
  }
}

// Message-related operations
export async function createMessage(messageData: {
  booking: string;
  content: string;
}): Promise<{
  success: boolean;
  message?: Message;
  error?: string;
}> {
  try {
    const currentUser = pocketbase.authStore.model;
    if (!currentUser) {
      return { success: false, error: 'User not authenticated' };
    }

    const createdMessage = await pocketbase.collection('messages').create({
      ...messageData,
      sender: currentUser.id
    });

    // Get the message with expanded relations
    const fullMessage = await pocketbase.collection('messages').getOne(createdMessage.id, {
      expand: 'sender,booking,booking.venue,booking.renter,booking.owner'
    });

    // Transform to Message type
    const message: Message = {
      id: fullMessage.id,
      booking: {
        id: fullMessage.expand?.booking?.id || fullMessage.booking,
        venue: {
          id: fullMessage.expand?.booking?.expand?.venue?.id || '',
          title: fullMessage.expand?.booking?.expand?.venue?.title || ''
        },
        renter: {
          id: fullMessage.expand?.booking?.expand?.renter?.id || fullMessage.expand?.booking?.renter || '',
          name: fullMessage.expand?.booking?.expand?.renter?.name || ''
        },
        owner: {
          id: fullMessage.expand?.booking?.expand?.owner?.id || fullMessage.expand?.booking?.owner || '',
          name: fullMessage.expand?.booking?.expand?.owner?.name || ''
        }
      },
      sender: {
        id: fullMessage.expand?.sender?.id || fullMessage.sender,
        name: fullMessage.expand?.sender?.name || '',
        email: fullMessage.expand?.sender?.email || '',
        avatar: fullMessage.expand?.sender?.avatar
      },
      content: fullMessage.content,
      created: fullMessage.created,
      updated: fullMessage.updated
    };

    console.log('Message created successfully:', message.id);
    return { success: true, message };
  } catch (error) {
    console.error('Failed to create message:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create message'
    };
  }
}

export async function getBookingMessages(bookingId: string, page = 1, perPage = 50): Promise<{
  success: boolean;
  messages: Message[];
  totalPages: number;
  totalItems: number;
  error?: string;
}> {
  try {
    const result = await pocketbase.collection('messages').getList(page, perPage, {
      filter: `booking = "${bookingId}"`,
      sort: 'created',
      expand: 'sender,booking,booking.venue,booking.renter,booking.owner'
    });

    // Transform RecordModel to Message type
    const messages: Message[] = result.items.map((item: RecordModel) => ({
      id: item.id,
      booking: {
        id: item.expand?.booking?.id || item.booking,
        venue: {
          id: item.expand?.booking?.expand?.venue?.id || '',
          title: item.expand?.booking?.expand?.venue?.title || ''
        },
        renter: {
          id: item.expand?.booking?.expand?.renter?.id || item.expand?.booking?.renter || '',
          name: item.expand?.booking?.expand?.renter?.name || ''
        },
        owner: {
          id: item.expand?.booking?.expand?.owner?.id || item.expand?.booking?.owner || '',
          name: item.expand?.booking?.expand?.owner?.name || ''
        }
      },
      sender: {
        id: item.expand?.sender?.id || item.sender,
        name: item.expand?.sender?.name || '',
        email: item.expand?.sender?.email || '',
        avatar: item.expand?.sender?.avatar
      },
      content: item.content,
      created: item.created,
      updated: item.updated
    }));

    return {
      success: true,
      messages,
      totalPages: result.totalPages,
      totalItems: result.totalItems
    };
  } catch (error) {
    console.error('Failed to get booking messages:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get messages',
      messages: [],
      totalPages: 0,
      totalItems: 0
    };
  }
}

export async function subscribeToBookingMessages(
  bookingId: string,
  callback: (data: { action: string; record: Message }) => void
): Promise<{
  success: boolean;
  unsubscribe?: () => void;
  error?: string;
}> {
  try {
    // Subscribe to messages for this specific booking
    const unsubscribe = await pocketbase.collection('messages').subscribe('*', async (e) => {
      // Only process messages for this booking
      if (e.record?.booking === bookingId) {
        // Get the full message with expanded relations
        try {
          const fullMessage = await pocketbase.collection('messages').getOne(e.record.id, {
            expand: 'sender,booking,booking.venue,booking.renter,booking.owner'
          });

          // Transform to Message type
          const message: Message = {
            id: fullMessage.id,
            booking: {
              id: fullMessage.expand?.booking?.id || fullMessage.booking,
              venue: {
                id: fullMessage.expand?.booking?.expand?.venue?.id || '',
                title: fullMessage.expand?.booking?.expand?.venue?.title || ''
              },
              renter: {
                id: fullMessage.expand?.booking?.expand?.renter?.id || fullMessage.expand?.booking?.renter || '',
                name: fullMessage.expand?.booking?.expand?.renter?.name || ''
              },
              owner: {
                id: fullMessage.expand?.booking?.expand?.owner?.id || fullMessage.expand?.booking?.owner || '',
                name: fullMessage.expand?.booking?.expand?.owner?.name || ''
              }
            },
            sender: {
              id: fullMessage.expand?.sender?.id || fullMessage.sender,
              name: fullMessage.expand?.sender?.name || '',
              email: fullMessage.expand?.sender?.email || '',
              avatar: fullMessage.expand?.sender?.avatar
            },
            content: fullMessage.content,
            created: fullMessage.created,
            updated: fullMessage.updated
          };

          callback({
            action: e.action,
            record: message
          });
        } catch (expandError) {
          console.error('Failed to expand message:', expandError);
          // Fallback to basic record if expansion fails
          const basicMessage: Message = {
            id: e.record.id,
            booking: {
              id: e.record.booking,
              venue: { id: '', title: '' },
              renter: { id: '', name: '' },
              owner: { id: '', name: '' }
            },
            sender: {
              id: e.record.sender,
              name: '',
              email: '',
            },
            content: e.record.content,
            created: e.record.created,
            updated: e.record.updated
          };

          callback({
            action: e.action,
            record: basicMessage
          });
        }
      }
    }, {
      filter: `booking = "${bookingId}"`
    });

    return { success: true, unsubscribe };
  } catch (error) {
    console.error('Failed to subscribe to messages:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to subscribe to messages'
    };
  }
}

export async function updateMessage(messageId: string, content: string): Promise<{
  success: boolean;
  message?: RecordModel;
  error?: string;
}> {
  try {
    const message = await pocketbase.collection('messages').update(messageId, {
      content
    });

    console.log('Message updated successfully:', messageId);
    return { success: true, message };
  } catch (error) {
    console.error('Failed to update message:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update message'
    };
  }
}

export async function deleteMessage(messageId: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    await pocketbase.collection('messages').delete(messageId);
    console.log('Message deleted successfully:', messageId);
    return { success: true };
  } catch (error) {
    console.error('Failed to delete message:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete message'
    };
  }
}

// Review-related operations
export async function createReview(reviewData: ReviewFormData): Promise<CreateReviewResponse> {
  try {
    const currentUser = pocketbase.authStore.model;
    if (!currentUser) {
      return { success: false, error: 'User not authenticated' };
    }

    // Verify the user is the renter for this booking
    if (currentUser.id !== reviewData.renter) {
      return { success: false, error: 'Unauthorized: You can only review your own bookings' };
    }

    // Check if booking is completed
    const booking = await pocketbase.collection('bookings').getOne(reviewData.booking);
    if (booking.status !== 'completed') {
      return { success: false, error: 'You can only review completed bookings' };
    }

    // Check if review already exists for this booking
    try {
      const existingReview = await pocketbase.collection('reviews').getFirstListItem(`booking="${reviewData.booking}"`);
      if (existingReview) {
        return { success: false, error: 'You have already reviewed this booking' };
      }
    } catch (_error) {
      // No existing review found, which is what we want
    }

    const createdReview = await pocketbase.collection('reviews').create(reviewData);
    console.log('Review created successfully:', createdReview.id);

    // Update venue average rating
    await updateVenueRating(reviewData.venue);

    return { success: true, review: createdReview as Review };
  } catch (error) {
    console.error('Failed to create review:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create review'
    };
  }
}

export async function getVenueReviews(venueId: string, page: number = 1, perPage: number = 10): Promise<GetReviewsResponse> {
  try {
    const reviews = await pocketbase.collection('reviews').getList(page, perPage, {
      filter: `venue="${venueId}"`,
      sort: '-created',
      expand: 'renter,booking,venue'
    });

    return { success: true, reviews: reviews as ReviewListResponse };
  } catch (error) {
    console.error('Failed to get venue reviews:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get reviews'
    };
  }
}

export async function addOwnerResponse(reviewId: string, responseData: OwnerResponseData): Promise<UpdateReviewResponse> {
  try {
    const currentUser = pocketbase.authStore.model;
    if (!currentUser) {
      return { success: false, error: 'User not authenticated' };
    }

    // Get the review to verify ownership
    const review = await pocketbase.collection('reviews').getOne(reviewId, {
      expand: 'venue'
    });

    // Check if user is the venue owner
    if (review.expand?.venue?.owner !== currentUser.id) {
      return { success: false, error: 'Unauthorized: Only venue owners can respond to reviews' };
    }

    // Check if response already exists
    if (review.owner_response && review.owner_response.trim() !== '') {
      return { success: false, error: 'You have already responded to this review' };
    }

    const updatedReview = await pocketbase.collection('reviews').update(reviewId, responseData);
    console.log('Owner response added successfully:', reviewId);

    return { success: true, review: updatedReview as Review };
  } catch (error) {
    console.error('Failed to add owner response:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to add response'
    };
  }
}

export async function getVenueRatingStats(venueId: string): Promise<GetReviewStatsResponse> {
  try {
    const reviews = await pocketbase.collection('reviews').getFullList({
      filter: `venue="${venueId}"`
    });

    if (reviews.length === 0) {
      return {
        success: true,
        stats: {
          averageRating: 0,
          totalReviews: 0,
          ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
        }
      };
    }

    const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
    const averageRating = totalRating / reviews.length;

    const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    reviews.forEach(review => {
      ratingDistribution[review.rating as keyof typeof ratingDistribution]++;
    });

    return {
      success: true,
      stats: {
        averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
        totalReviews: reviews.length,
        ratingDistribution
      }
    };
  } catch (error) {
    console.error('Failed to get venue rating stats:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get rating stats'
    };
  }
}

export async function updateVenueRating(venueId: string): Promise<void> {
  try {
    const statsResult = await getVenueRatingStats(venueId);
    if (statsResult.success && statsResult.stats) {
      await pocketbase.collection('venues').update(venueId, {
        average_rating: statsResult.stats.averageRating,
        review_count: statsResult.stats.totalReviews
      });
      console.log('Venue rating updated:', venueId, statsResult.stats.averageRating);
    }
  } catch (error) {
    console.error('Failed to update venue rating:', error);
  }
}

export async function getEligibleBookingsForReview(userId: string): Promise<GetReviewPromptsResponse> {
  try {
    // Get completed bookings for the user that haven't been reviewed
    const bookings = await pocketbase.collection('bookings').getList(1, 50, {
      filter: `renter="${userId}" && status="completed"`,
      expand: 'venue',
      sort: '-end_date'
    });

    const prompts: ReviewPrompt[] = [];

    for (const booking of bookings.items) {
      // Check if this booking has already been reviewed
      try {
        await pocketbase.collection('reviews').getFirstListItem(`booking="${booking.id}"`);
        // Review exists, skip this booking
      } catch (_error) {
        // No review found, this booking is eligible
        prompts.push({
          bookingId: booking.id,
          venueId: booking.venue,
          venueName: booking.expand?.venue?.title || 'Unknown Venue',
          canReview: true,
          hasReviewed: false
        });
      }
    }

    return { success: true, prompts };
  } catch (error) {
    console.error('Failed to get eligible bookings for review:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get eligible bookings'
    };
  }
}

// Verification-related operations
export async function requestEmailVerification(): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const currentUser = pocketbase.authStore.model;
    if (!currentUser) {
      return { success: false, error: 'User not authenticated' };
    }

    await pocketbase.collection('users').requestVerification(currentUser.email);
    console.log('Verification email requested for:', currentUser.email);
    return { success: true };
  } catch (error) {
    console.error('Failed to request email verification:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to request verification email'
    };
  }
}

export async function confirmEmailVerification(token: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    await pocketbase.collection('users').confirmVerification(token);
    console.log('Email verification confirmed');
    return { success: true };
  } catch (error) {
    console.error('Failed to confirm email verification:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to confirm verification'
    };
  }
}

// Password reset operations
export async function requestPasswordReset(email: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    await pocketbase.collection('users').requestPasswordReset(email);
    console.log('Password reset email requested for:', email);
    return { success: true };
  } catch (error) {
    console.error('Failed to request password reset:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to request password reset'
    };
  }
}

export async function confirmPasswordReset(
  token: string,
  password: string,
  passwordConfirm: string
): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    await pocketbase.collection('users').confirmPasswordReset(
      token,
      password,
      passwordConfirm
    );
    console.log('Password reset confirmed');
    return { success: true };
  } catch (error) {
    console.error('Failed to confirm password reset:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to confirm password reset'
    };
  }
}

export async function isUserVerified(): Promise<boolean> {
  try {
    const currentUser = pocketbase.authStore.model;
    if (!currentUser) {
      return false;
    }

    // Get fresh user data to check verification status
    const user = await pocketbase.collection('users').getOne(currentUser.id);
    return user.verified || false;
  } catch (error) {
    console.error('Failed to check user verification status:', error);
    return false;
  }
}

// Checklist submission operations
export async function createChecklistSubmission(submissionData: {
  booking: string;
  submission_data: Record<string, boolean>;
  notes?: string;
}): Promise<{
  success: boolean;
  submission?: RecordModel;
  error?: string;
}> {
  try {
    const currentUser = pocketbase.authStore.model;
    if (!currentUser) {
      return { success: false, error: 'User not authenticated' };
    }

    const submission = await pocketbase.collection('checklist_submissions').create({
      ...submissionData,
      renter: currentUser.id
    });

    console.log('Checklist submission created successfully:', submission.id);
    return { success: true, submission };
  } catch (error) {
    console.error('Failed to create checklist submission:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create checklist submission'
    };
  }
}

export async function getChecklistSubmission(bookingId: string): Promise<{
  success: boolean;
  submission?: RecordModel;
  error?: string;
}> {
  try {
    const submission = await pocketbase.collection('checklist_submissions').getFirstListItem(
      `booking="${bookingId}"`,
      { expand: 'renter,booking' }
    );

    return { success: true, submission };
  } catch (error) {
    // If no submission found, that's not an error - just return success: false
    if (error instanceof Error && error.message.includes('no rows')) {
      return { success: false };
    }

    console.error('Failed to get checklist submission:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get checklist submission'
    };
  }
}

// Invitation operations (communicates with Deno service)
export async function sendGuestInvitations(invitationData: {
  bookingId: string;
  message: string;
  emails: string[];
}): Promise<{
  success: boolean;
  sentCount?: number;
  error?: string;
}> {
  try {
    // Get the Deno service URL from environment variables
    const denoServiceUrl = import.meta.env.PUBLIC_DENO_SERVICE_URL || 'http://localhost:8000';

    console.log('Sending guest invitations:', invitationData);

    const response = await fetch(`${denoServiceUrl}/api/internal/send-invitations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Add authentication if needed
      },
      body: JSON.stringify(invitationData)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new Error(errorData.error || `HTTP ${response.status}`);
    }

    const result = await response.json();

    return {
      success: true,
      sentCount: result.sentCount || invitationData.emails.length
    };
  } catch (error) {
    console.error('Failed to send guest invitations:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to send invitations'
    };
  }
}

// Export types for TypeScript support
export type { RecordModel } from 'pocketbase';
export default pocketbase;
