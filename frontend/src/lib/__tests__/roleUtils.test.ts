// Role Detection System Examples
// This file demonstrates how the role detection system works

import {
  getExplicitUserRoles,
  hasRole,
  getDashboardFeatures,
  getNavigationItems,
  canAccessAdminFeatures
} from '../roleUtils.ts';
import type { User } from '../../types/user.ts';

// Example usage of role detection functions

// Example 1: Basic renter user
export function testBasicRenter() {
  const renterUser: User = {
    id: '1',
    email: '<EMAIL>',
    created: '2023-01-01',
    updated: '2023-01-01',
    collectionId: 'users',
    collectionName: 'users',
    roles: ['renter']
  };

  const roleInfo = getExplicitUserRoles(renterUser);
  console.log('Renter role info:', roleInfo);

  const features = getDashboardFeatures(roleInfo);
  console.log('Renter dashboard features:', features);

  const navItems = getNavigationItems(roleInfo);
  console.log('Renter navigation items:', navItems);
}

// Example 2: Venue owner user
export function testVenueOwner() {
  const ownerUser: User = {
    id: '2',
    email: '<EMAIL>',
    created: '2023-01-01',
    updated: '2023-01-01',
    collectionId: 'users',
    collectionName: 'users',
    roles: ['owner', 'renter']
  };

  const roleInfo = getExplicitUserRoles(ownerUser);
  console.log('Owner role info:', roleInfo);

  const features = getDashboardFeatures(roleInfo);
  console.log('Owner dashboard features:', features);

  const navItems = getNavigationItems(roleInfo);
  console.log('Owner navigation items:', navItems);
}

// Example 3: Admin user
export function testAdminUser() {
  const adminUser: User = {
    id: '3',
    email: '<EMAIL>',
    created: '2023-01-01',
    updated: '2023-01-01',
    collectionId: 'users',
    collectionName: 'users',
    roles: ['admin']
  };

  const roleInfo = getExplicitUserRoles(adminUser);
  console.log('Admin role info:', roleInfo);

  const features = getDashboardFeatures(roleInfo);
  console.log('Admin dashboard features:', features);

  const navItems = getNavigationItems(roleInfo);
  console.log('Admin navigation items:', navItems);

  const canAccess = canAccessAdminFeatures(adminUser);
  console.log('Can access admin features:', canAccess);
}

// Example 4: Role checking
export function testRoleChecking() {
  const dualRoleUser: User = {
    id: '4',
    email: '<EMAIL>',
    created: '2023-01-01',
    updated: '2023-01-01',
    collectionId: 'users',
    collectionName: 'users',
    roles: ['owner', 'renter']
  };

  console.log('Is renter:', hasRole(dualRoleUser, 'renter'));
  console.log('Is owner:', hasRole(dualRoleUser, 'owner'));
  console.log('Is admin:', hasRole(dualRoleUser, 'admin'));
}

// Run examples (uncomment to test)
// testBasicRenter();
// testVenueOwner();
// testAdminUser();
// testRoleChecking();


