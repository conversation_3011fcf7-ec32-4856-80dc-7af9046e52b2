---
import Layout from '../../components/core/Layout.astro';
import VenueForm from '../../components/venues/VenueForm.tsx';
---

<Layout
  title="List Your Venue - Trodoo"
  description="List your property on Trodoo and start earning from your space."
>
  <div class="min-h-screen bg-slate-50">
    <!-- Hero Section -->
    <div class="bg-gradient-to-br from-primary-green to-primary-greenLight">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-16 text-center">
          <h1 class="text-4xl font-bold text-white mb-4">
            List Your Venue
          </h1>
          <p class="text-xl text-white/90 max-w-2xl mx-auto">
            Share your space with event hosts and start earning. It's easy to get started.
          </p>
        </div>
      </div>
    </div>

    <!-- Form Section -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <VenueForm
        client:load
        onCancel={() => {
          window.location.href = '/dashboard';
        }}
      />
    </div>
  </div>
</Layout>

<script>
  // Additional client-side functionality
  document.addEventListener('DOMContentLoaded', async () => {
    console.log('Venue creation page loaded');

    try {
      // Initialize authentication state
      const { initializeAuth } = await import('../../lib/state.ts');
      initializeAuth();
      console.log('Authentication initialized for venue creation page');

      // Import PocketBase functions and create venue creation handler
      const { createVenue, isAuthenticated, getCurrentUser } = await import('../../lib/pocketbase.ts');
      const { VenueFormData } = await import('../../types/venue.ts');

      // Create global venue creation handler
      (window as any).handleVenueSubmission = async function(venueData: any) {
        console.log('=== VENUE CREATION HANDLER CALLED ===');
        console.log('Venue creation - Checking authentication...');
        console.log('Is authenticated:', isAuthenticated());
        console.log('Current user:', getCurrentUser());
        console.log('Venue form data:', venueData);

        try {
          if (!isAuthenticated()) {
            alert('You must be logged in to create a venue. Please sign in and try again.');
            window.location.href = '/auth/login?redirect=/venues/new';
            return;
          }

          // Validate form data before submission
          if (!venueData.title?.trim()) {
            alert('Please enter a venue title.');
            return;
          }
          if (!venueData.description?.trim()) {
            alert('Please enter a venue description.');
            return;
          }
          if (!venueData.address?.street?.trim() || !venueData.address?.city?.trim()) {
            alert('Please enter a complete address.');
            return;
          }
          if (!venueData.capacity || venueData.capacity <= 0) {
            alert('Please enter a valid capacity.');
            return;
          }
          if (!venueData.price_per_hour || venueData.price_per_hour <= 0) {
            alert('Please enter a valid price per hour.');
            return;
          }

          console.log('Venue creation - Starting venue creation process...');
          const result = await createVenue(venueData);

          if (result.success) {
            alert('Venue created successfully! Redirecting to your dashboard...');
            window.location.href = '/dashboard';
          } else {
            console.error('Venue creation failed:', result.error);
            alert(`Failed to create venue: ${result.error}`);
          }
        } catch (error) {
          console.error('Error creating venue:', error);
          alert('An unexpected error occurred. Please try again.');
        }
      };

      console.log('✅ Global venue submission handler created and ready');

      // Add test function for debugging
      (window as any).testVenueHandler = function() {
        console.log('Testing venue handler availability...');
        if (typeof (window as any).handleVenueSubmission === 'function') {
          console.log('✅ Handler is available and is a function');
          return true;
        } else {
          console.log('❌ Handler is not available or not a function');
          return false;
        }
      };

      // Test the handler immediately
      (window as any).testVenueHandler();
    } catch (error) {
      console.error('Error initializing venue creation page:', error);
      if (error instanceof Error) {
        console.error('Error details:', error.message);
        console.error('Error stack:', error.stack);
      }

      // Fallback: try to create a basic handler
      try {
        (window as any).handleVenueSubmission = async function(_venueData: any) {
          alert('Venue creation handler not properly initialized. Please refresh the page and try again.');
          console.error('Fallback handler called - main handler failed to initialize');
        };
        console.log('⚠️ Fallback venue submission handler created');
      } catch (fallbackError) {
        console.error('Failed to create fallback handler:', fallbackError);
      }
    }
  });
</script>
