import { createVenue, isAuthenticated, getCurrentUser } from '../lib/pocketbase.ts';
import type { VenueFormData } from '../types/venue.ts';

// Global venue creation handler
export async function handleVenueSubmission(venueData: VenueFormData): Promise<void> {
  console.log('=== VENUE CREATION HANDLER CALLED ===');
  console.log('Venue creation - Checking authentication...');
  console.log('Is authenticated:', isAuthenticated());
  console.log('Current user:', getCurrentUser());
  console.log('Venue form data:', venueData);

  try {
    if (!isAuthenticated()) {
      alert('You must be logged in to create a venue. Please sign in and try again.');
      window.location.href = '/auth/login?redirect=/venues/new';
      return;
    }

    // Validate form data before submission
    if (!venueData.title?.trim()) {
      alert('Please enter a venue title.');
      return;
    }
    if (!venueData.description?.trim()) {
      alert('Please enter a venue description.');
      return;
    }
    if (!venueData.address?.street?.trim() || !venueData.address?.city?.trim()) {
      alert('Please enter a complete address.');
      return;
    }
    if (!venueData.capacity || venueData.capacity <= 0) {
      alert('Please enter a valid capacity.');
      return;
    }
    if (!venueData.price_per_hour || venueData.price_per_hour <= 0) {
      alert('Please enter a valid price per hour.');
      return;
    }

    console.log('Venue creation - Starting venue creation process...');
    const result = await createVenue(venueData);

    if (result.success) {
      alert('Venue created successfully! Redirecting to your dashboard...');
      window.location.href = '/dashboard';
    } else {
      console.error('Venue creation failed:', result.error);
      alert(`Failed to create venue: ${result.error}`);
    }
  } catch (error) {
    console.error('Error creating venue:', error);
    alert('An unexpected error occurred. Please try again.');
  }
}

// Make the handler available globally
declare global {
  interface Window {
    handleVenueSubmission: typeof handleVenueSubmission;
  }
}

if (typeof window !== 'undefined') {
  window.handleVenueSubmission = handleVenueSubmission;
}
